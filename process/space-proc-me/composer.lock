{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "67d00ecfd76e918a10c4a41e946a2396", "packages": [{"name": "amphp/amp", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/amphp/amp.git", "reference": "ded3d9be08f526089eb7ee8d9f16a9768f9dec2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/amp/zipball/ded3d9be08f526089eb7ee8d9f16a9768f9dec2d", "reference": "ded3d9be08f526089eb7ee8d9f16a9768f9dec2d", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^7 | ^8 | ^9", "react/promise": "^2", "vimeo/psalm": "^3.12"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"files": ["lib/functions.php", "lib/Internal/functions.php"], "psr-4": {"Amp\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A non-blocking concurrency framework for PHP applications.", "homepage": "https://amphp.org/amp", "keywords": ["async", "asynchronous", "awaitable", "concurrency", "event", "event-loop", "future", "non-blocking", "promise"], "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.4"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T18:52:26+00:00"}, {"name": "amphp/byte-stream", "version": "v1.8.2", "source": {"type": "git", "url": "https://github.com/amphp/byte-stream.git", "reference": "4f0e968ba3798a423730f567b1b50d3441c16ddc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/byte-stream/zipball/4f0e968ba3798a423730f567b1b50d3441c16ddc", "reference": "4f0e968ba3798a423730f567b1b50d3441c16ddc", "shasum": ""}, "require": {"amphp/amp": "^2", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.4", "friendsofphp/php-cs-fixer": "^2.3", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^6 || ^7 || ^8", "psalm/phar": "^3.11.4"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\ByteStream\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A stream abstraction to make working with non-blocking I/O simple.", "homepage": "https://amphp.org/byte-stream", "keywords": ["amp", "amphp", "async", "io", "non-blocking", "stream"], "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.8.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-04-13T18:00:56+00:00"}, {"name": "amphp/cache", "version": "v1.5.1", "source": {"type": "git", "url": "https://github.com/amphp/cache.git", "reference": "fe78cfae2fb8c92735629b8cd1893029c73c9b63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/cache/zipball/fe78cfae2fb8c92735629b8cd1893029c73c9b63", "reference": "fe78cfae2fb8c92735629b8cd1893029c73c9b63", "shasum": ""}, "require": {"amphp/amp": "^2", "amphp/serialization": "^1", "amphp/sync": "^1.2", "php": ">=7.1"}, "conflict": {"amphp/file": "<0.2 || >=3"}, "require-dev": {"amphp/file": "^1 || ^2", "amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.1", "phpunit/phpunit": "^6 | ^7 | ^8 | ^9", "vimeo/psalm": "^4"}, "type": "library", "autoload": {"psr-4": {"Amp\\Cache\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A promise-aware caching API for Amp.", "homepage": "https://github.com/amphp/cache", "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/cache/issues", "source": "https://github.com/amphp/cache/tree/v1.5.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T19:35:02+00:00"}, {"name": "amphp/dns", "version": "v1.2.4", "source": {"type": "git", "url": "https://github.com/amphp/dns.git", "reference": "4a13ffdc5e088593eb01860fc5002ebd9316d562"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/dns/zipball/4a13ffdc5e088593eb01860fc5002ebd9316d562", "reference": "4a13ffdc5e088593eb01860fc5002ebd9316d562", "shasum": ""}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.1", "amphp/cache": "^1.2", "amphp/parser": "^1", "amphp/windows-registry": "^0.3", "daverandom/libdns": "^2.0.1", "ext-filter": "*", "ext-json": "*", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "phpunit/phpunit": "^6 || ^7 || ^8 || ^9"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\Dns\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Async DNS resolution for Amp.", "homepage": "https://github.com/amphp/dns", "keywords": ["amp", "amphp", "async", "client", "dns", "resolve"], "support": {"issues": "https://github.com/amphp/dns/issues", "source": "https://github.com/amphp/dns/tree/v1.2.4"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-12-08T15:06:32+00:00"}, {"name": "amphp/hpack", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/amphp/hpack.git", "reference": "4f293064b15682a2b178b1367ddf0b8b5feb0239"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/hpack/zipball/4f293064b15682a2b178b1367ddf0b8b5feb0239", "reference": "4f293064b15682a2b178b1367ddf0b8b5feb0239", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "http2jp/hpack-test-case": "^1", "nikic/php-fuzzer": "^0.0.10", "phpunit/phpunit": "^7 | ^8 | ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Amp\\Http\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTTP/2 HPack implementation.", "homepage": "https://github.com/amphp/hpack", "keywords": ["headers", "hpack", "http-2"], "support": {"issues": "https://github.com/amphp/hpack/issues", "source": "https://github.com/amphp/hpack/tree/v3.2.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T19:00:16+00:00"}, {"name": "amphp/http", "version": "v1.7.3", "source": {"type": "git", "url": "https://github.com/amphp/http.git", "reference": "3a33e68a3b53f7279217238e89748cf0cb30b8a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/http/zipball/3a33e68a3b53f7279217238e89748cf0cb30b8a6", "reference": "3a33e68a3b53f7279217238e89748cf0cb30b8a6", "shasum": ""}, "require": {"amphp/hpack": "^3", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "2.x-dev#3c24119d0377eed2093d5c0f0541478cb75ea72d", "phpunit/phpunit": "^9 || ^8 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Http\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Basic HTTP primitives which can be shared by servers and clients.", "support": {"issues": "https://github.com/amphp/http/issues", "source": "https://github.com/amphp/http/tree/v1.7.3"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-04-03T17:45:14+00:00"}, {"name": "amphp/http-client", "version": "v4.6.5", "source": {"type": "git", "url": "https://github.com/amphp/http-client.git", "reference": "6cac9e172a66a04df305acc8de02b6c40832b269"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/http-client/zipball/6cac9e172a66a04df305acc8de02b6c40832b269", "reference": "6cac9e172a66a04df305acc8de02b6c40832b269", "shasum": ""}, "require": {"amphp/amp": "^2.4", "amphp/byte-stream": "^1.6", "amphp/hpack": "^3", "amphp/http": "^1.6", "amphp/socket": "^1", "amphp/sync": "^1.3", "league/uri": "^6 | ^7", "php": ">=7.2", "psr/http-message": "^1 | ^2"}, "conflict": {"amphp/file": "<0.2 || >=3"}, "require-dev": {"amphp/file": "^0.2 || ^0.3 || ^1 || ^2", "amphp/http-server": "^2", "amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.1", "amphp/react-adapter": "^2.1", "clue/socks-react": "^1.0", "ext-json": "*", "kelunik/link-header-rfc5988": "^1.0", "laminas/laminas-diactoros": "^2.3", "phpunit/phpunit": "^7 || ^8 || ^9", "vimeo/psalm": "^5"}, "suggest": {"amphp/file": "Required for file request bodies and HTTP archive logging", "ext-json": "Required for logging HTTP archives", "ext-zlib": "Allows using compression for response bodies."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"files": ["src/Internal/functions.php"], "psr-4": {"Amp\\Http\\Client\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Asynchronous concurrent HTTP/2 and HTTP/1.1 client built on the Amp concurrency framework", "homepage": "https://github.com/amphp/http-client", "keywords": ["async", "client", "concurrent", "http", "non-blocking", "rest"], "support": {"issues": "https://github.com/amphp/http-client/issues", "source": "https://github.com/amphp/http-client/tree/v4.6.5"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-12-13T15:50:23+00:00"}, {"name": "amphp/parser", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/amphp/parser.git", "reference": "3cf1f8b32a0171d4b1bed93d25617637a77cded7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/parser/zipball/3cf1f8b32a0171d4b1bed93d25617637a77cded7", "reference": "3cf1f8b32a0171d4b1bed93d25617637a77cded7", "shasum": ""}, "require": {"php": ">=7.4"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "phpunit/phpunit": "^9", "psalm/phar": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Amp\\Parser\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A generator parser to make streaming parsers simple.", "homepage": "https://github.com/amphp/parser", "keywords": ["async", "non-blocking", "parser", "stream"], "support": {"issues": "https://github.com/amphp/parser/issues", "source": "https://github.com/amphp/parser/tree/v1.1.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T19:16:53+00:00"}, {"name": "amphp/process", "version": "v1.1.9", "source": {"type": "git", "url": "https://github.com/amphp/process.git", "reference": "55b837d4f1857b9bd7efb7bb859ae6b0e804f13f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/process/zipball/55b837d4f1857b9bd7efb7bb859ae6b0e804f13f", "reference": "55b837d4f1857b9bd7efb7bb859ae6b0e804f13f", "shasum": ""}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.4", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "phpunit/phpunit": "^6"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\Process\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Asynchronous process manager.", "homepage": "https://github.com/amphp/process", "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v1.1.9"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-12-13T17:38:25+00:00"}, {"name": "amphp/serialization", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/amphp/serialization.git", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/serialization/zipball/693e77b2fb0b266c3c7d622317f881de44ae94a1", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "phpunit/phpunit": "^9 || ^8 || ^7"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Serialization\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Serialization tools for IPC and data storage in PHP.", "homepage": "https://github.com/amphp/serialization", "keywords": ["async", "asynchronous", "serialization", "serialize"], "support": {"issues": "https://github.com/amphp/serialization/issues", "source": "https://github.com/amphp/serialization/tree/master"}, "time": "2020-03-25T21:39:07+00:00"}, {"name": "amphp/socket", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/amphp/socket.git", "reference": "b00528bd75548b7ae06a502358bb3ff8b106f5ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/socket/zipball/b00528bd75548b7ae06a502358bb3ff8b106f5ab", "reference": "b00528bd75548b7ae06a502358bb3ff8b106f5ab", "shasum": ""}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.6", "amphp/dns": "^1 || ^0.9", "ext-openssl": "*", "kelunik/certificate": "^1.1", "league/uri-parser": "^1.4", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "phpunit/phpunit": "^6 || ^7 || ^8", "vimeo/psalm": "^3.9@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/functions.php", "src/Internal/functions.php"], "psr-4": {"Amp\\Socket\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Async socket connection / server tools for Amp.", "homepage": "https://github.com/amphp/socket", "keywords": ["amp", "async", "encryption", "non-blocking", "sockets", "tcp", "tls"], "support": {"issues": "https://github.com/amphp/socket/issues", "source": "https://github.com/amphp/socket/tree/v1.2.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T18:12:22+00:00"}, {"name": "amphp/sync", "version": "v1.4.2", "source": {"type": "git", "url": "https://github.com/amphp/sync.git", "reference": "85ab06764f4f36d63b1356b466df6111cf4b89cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/sync/zipball/85ab06764f4f36d63b1356b466df6111cf4b89cf", "reference": "85ab06764f4f36d63b1356b466df6111cf4b89cf", "shasum": ""}, "require": {"amphp/amp": "^2.2", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.1", "phpunit/phpunit": "^9 || ^8 || ^7"}, "type": "library", "autoload": {"files": ["src/functions.php", "src/ConcurrentIterator/functions.php"], "psr-4": {"Amp\\Sync\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mutex, Semaphore, and other synchronization tools for Amp.", "homepage": "https://github.com/amphp/sync", "keywords": ["async", "asynchronous", "mutex", "semaphore", "synchronization"], "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v1.4.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2021-10-25T18:29:10+00:00"}, {"name": "amphp/windows-registry", "version": "v0.3.3", "source": {"type": "git", "url": "https://github.com/amphp/windows-registry.git", "reference": "0f56438b9197e224325e88f305346f0221df1f71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/windows-registry/zipball/0f56438b9197e224325e88f305346f0221df1f71", "reference": "0f56438b9197e224325e88f305346f0221df1f71", "shasum": ""}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.4", "amphp/process": "^1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master"}, "type": "library", "autoload": {"psr-4": {"Amp\\WindowsRegistry\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Windows Registry Reader.", "support": {"issues": "https://github.com/amphp/windows-registry/issues", "source": "https://github.com/amphp/windows-registry/tree/master"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2020-07-10T16:13:29+00:00"}, {"name": "daverandom/libdns", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/DaveRandom/LibDNS.git", "reference": "b84c94e8fe6b7ee4aecfe121bfe3b6177d303c8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DaveRandom/LibDNS/zipball/b84c94e8fe6b7ee4aecfe121bfe3b6177d303c8a", "reference": "b84c94e8fe6b7ee4aecfe121bfe3b6177d303c8a", "shasum": ""}, "require": {"ext-ctype": "*", "php": ">=7.1"}, "suggest": {"ext-intl": "Required for IDN support"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"LibDNS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "DNS protocol implementation written in pure PHP", "keywords": ["dns"], "support": {"issues": "https://github.com/DaveRandom/LibDNS/issues", "source": "https://github.com/DaveRandom/LibDNS/tree/v2.1.0"}, "time": "2024-04-12T12:12:48+00:00"}, {"name": "doctrine/annotations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f", "reference": "e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f", "shasum": ""}, "require": {"doctrine/lexer": "^2 || ^3", "ext-tokenizer": "*", "php": "^7.2 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.1"}, "time": "2023-02-02T22:02:53+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/collections", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2eb07e5953eed811ce1b309a7478a3b236f2273d", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d", "shasum": ""}, "require": {"doctrine/deprecations": "^1", "php": "^8.1", "symfony/polyfill-php84": "^1.30"}, "require-dev": {"doctrine/coding-standard": "^12", "ext-json": "*", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2025-03-22T10:17:19+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "doctrine/mongodb-odm", "version": "2.11.2", "source": {"type": "git", "url": "https://github.com/doctrine/mongodb-odm.git", "reference": "9cc829197581df02c227f00cfa789b077949dffa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/mongodb-odm/zipball/9cc829197581df02c227f00cfa789b077949dffa", "reference": "9cc829197581df02c227f00cfa789b077949dffa", "shasum": ""}, "require": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/collections": "^1.5 || ^2.0", "doctrine/event-manager": "^1.0 || ^2.0", "doctrine/instantiator": "^1.1 || ^2", "doctrine/persistence": "^3.2 || ^4", "ext-mongodb": "^1.21 || ^2.0", "friendsofphp/proxy-manager-lts": "^1.0", "jean85/pretty-package-versions": "^1.3.0 || ^2.0.1", "mongodb/mongodb": "^1.21 || ^2.0@dev", "php": "^8.1", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/deprecation-contracts": "^2.2 || ^3.0", "symfony/var-dumper": "^5.4 || ^6.0 || ^7.0", "symfony/var-exporter": "^6.2 || ^7.0"}, "conflict": {"doctrine/annotations": "<1.12 || >=3.0"}, "require-dev": {"doctrine/annotations": "^1.12 || ^2.0", "doctrine/coding-standard": "^12.0", "doctrine/orm": "^3.2", "ext-bcmath": "*", "jmikola/geojson": "^1.0", "phpbench/phpbench": "^1.0.0", "phpstan/phpstan": "~1.10.67", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.4", "squizlabs/php_codesniffer": "^3.5", "symfony/cache": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"doctrine/annotations": "For annotation mapping support", "ext-bcmath": "Decimal128 type support"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\ODM\\MongoDB\\": "lib/Doctrine/ODM/MongoDB"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine MongoDB Object Document Mapper (ODM) provides transparent persistence for PHP objects to MongoDB.", "homepage": "https://www.doctrine-project.org/projects/mongodb-odm.html", "keywords": ["data", "mapper", "mapping", "mongodb", "object", "odm", "php"], "support": {"issues": "https://github.com/doctrine/mongodb-odm/issues", "source": "https://github.com/doctrine/mongodb-odm/tree/2.11.2"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fmongodb-odm", "type": "tidelift"}], "time": "2025-05-23T07:27:12+00:00"}, {"name": "doctrine/mongodb-odm-bundle", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMongoDBBundle.git", "reference": "e0a1b4342fb5f82abceef0098302b398ec2f67d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMongoDBBundle/zipball/e0a1b4342fb5f82abceef0098302b398ec2f67d7", "reference": "e0a1b4342fb5f82abceef0098302b398ec2f67d7", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "doctrine/mongodb-odm": "^2.6", "doctrine/persistence": "^3.0 || ^4.0", "ext-mongodb": "^1.16 || ^2", "php": "^8.1", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/config": "^6.4 || ^7.0", "symfony/console": "^6.4 || ^7.0", "symfony/dependency-injection": "^6.4 || ^7.0", "symfony/doctrine-bridge": "^6.4 || ^7.0", "symfony/framework-bundle": "^6.4 || ^7.0", "symfony/http-kernel": "^6.4 || ^7.0", "symfony/options-resolver": "^6.4 || ^7.0"}, "conflict": {"doctrine/data-fixtures": "<1.8 || >=3"}, "require-dev": {"composer/semver": "^3.4", "doctrine/coding-standard": "^11.0", "doctrine/data-fixtures": "^1.8 || ^2.0", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^9.5.5", "symfony/browser-kit": "^6.4 || ^7.0", "symfony/form": "^6.4 || ^7.0", "symfony/phpunit-bridge": "^6.4.1 || ^7.0.1", "symfony/security-bundle": "^6.4 || ^7.0", "symfony/stopwatch": "^6.4 || ^7.0", "symfony/validator": "^6.4 || ^7.0", "symfony/yaml": "^6.4 || ^7.0"}, "suggest": {"doctrine/data-fixtures": "Load data fixtures"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\MongoDBBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony Doctrine MongoDB Bundle", "homepage": "http://www.doctrine-project.org", "keywords": ["mongodb", "persistence", "symfony"], "support": {"issues": "https://github.com/doctrine/DoctrineMongoDBBundle/issues", "source": "https://github.com/doctrine/DoctrineMongoDBBundle/tree/5.3.0"}, "time": "2025-04-08T16:32:24+00:00"}, {"name": "doctrine/persistence", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "45004aca79189474f113cbe3a53847c2115a55fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/45004aca79189474f113cbe3a53847c2115a55fa", "reference": "45004aca79189474f113cbe3a53847c2115a55fa", "shasum": ""}, "require": {"doctrine/event-manager": "^1 || ^2", "php": "^8.1", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"doctrine/common": "<2.10"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^9.6", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://www.doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/4.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2024-11-01T21:49:07+00:00"}, {"name": "friendsofphp/proxy-manager-lts", "version": "v1.0.18", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/proxy-manager-lts.git", "reference": "2c8a6cffc3220e99352ad958fe7cf06bf6f7690f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/proxy-manager-lts/zipball/2c8a6cffc3220e99352ad958fe7cf06bf6f7690f", "reference": "2c8a6cffc3220e99352ad958fe7cf06bf6f7690f", "shasum": ""}, "require": {"laminas/laminas-code": "~3.4.1|^4.0", "php": ">=7.1", "symfony/filesystem": "^4.4.17|^5.0|^6.0|^7.0"}, "conflict": {"laminas/laminas-stdlib": "<3.2.1", "zendframework/zend-stdlib": "<3.2.1"}, "replace": {"ocramius/proxy-manager": "^2.1"}, "require-dev": {"ext-phar": "*", "symfony/phpunit-bridge": "^5.4|^6.0|^7.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/Ocramius/ProxyManager", "name": "ocramius/proxy-manager"}}, "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adding support for a wider range of PHP versions to ocramius/proxy-manager", "homepage": "https://github.com/FriendsOfPHP/proxy-manager-lts", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/FriendsOfPHP/proxy-manager-lts/issues", "source": "https://github.com/FriendsOfPHP/proxy-manager-lts/tree/v1.0.18"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2024-03-20T12:50:41+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "4d7aa5dab42e2a76d99559706022885de0e18e1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/4d7aa5dab42e2a76d99559706022885de0e18e1a", "reference": "4d7aa5dab42e2a76d99559706022885de0e18e1a", "shasum": ""}, "require": {"composer-runtime-api": "^2.1.0", "php": "^7.4|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^7.5|^8.5|^9.6", "rector/rector": "^2.0", "vimeo/psalm": "^4.3 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.1.1"}, "time": "2025-03-19T14:43:43+00:00"}, {"name": "jms/metadata", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/metadata.git", "reference": "7ca240dcac0c655eb15933ee55736ccd2ea0d7a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/metadata/zipball/7ca240dcac0c655eb15933ee55736ccd2ea0d7a6", "reference": "7ca240dcac0c655eb15933ee55736ccd2ea0d7a6", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "require-dev": {"doctrine/cache": "^1.0", "doctrine/coding-standard": "^8.0", "mikey179/vfsstream": "^1.6.7", "phpunit/phpunit": "^8.5|^9.0", "psr/container": "^1.0|^2.0", "symfony/cache": "^3.1|^4.0|^5.0", "symfony/dependency-injection": "^3.1|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Metadata\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Class/method/property metadata management in PHP", "keywords": ["annotations", "metadata", "xml", "yaml"], "support": {"issues": "https://github.com/schmittjoh/metadata/issues", "source": "https://github.com/schmittjoh/metadata/tree/2.8.0"}, "time": "2023-02-15T13:44:18+00:00"}, {"name": "jms/serializer", "version": "3.32.5", "source": {"type": "git", "url": "https://github.com/schmittjoh/serializer.git", "reference": "7c88b1b02ff868eecc870eeddbb3b1250e4bd89c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/serializer/zipball/7c88b1b02ff868eecc870eeddbb3b1250e4bd89c", "reference": "7c88b1b02ff868eecc870eeddbb3b1250e4bd89c", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1 || ^2.0", "doctrine/lexer": "^2.0 || ^3.0", "jms/metadata": "^2.6", "php": "^7.4 || ^8.0", "phpstan/phpdoc-parser": "^1.20 || ^2.0"}, "require-dev": {"doctrine/annotations": "^1.14 || ^2.0", "doctrine/coding-standard": "^12.0", "doctrine/orm": "^2.14 || ^3.0", "doctrine/persistence": "^2.5.2 || ^3.0", "doctrine/phpcr-odm": "^1.5.2 || ^2.0", "ext-pdo_sqlite": "*", "jackalope/jackalope-doctrine-dbal": "^1.3", "ocramius/proxy-manager": "^1.0 || ^2.0", "phpbench/phpbench": "^1.0", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^9.0 || ^10.0 || ^11.0", "psr/container": "^1.0 || ^2.0", "rector/rector": "^1.0.0 || ^2.0@dev", "slevomat/coding-standard": "dev-master#f2cc4c553eae68772624ffd7dd99022343b69c31 as 8.11.9999", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/expression-language": "^5.4 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0", "symfony/form": "^5.4 || ^6.0 || ^7.0", "symfony/translation": "^5.4 || ^6.0 || ^7.0", "symfony/uid": "^5.4 || ^6.0 || ^7.0", "symfony/validator": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0", "twig/twig": "^1.34 || ^2.4 || ^3.0"}, "suggest": {"doctrine/collections": "Required if you like to use doctrine collection types as ArrayCollection.", "symfony/cache": "Required if you like to use cache functionality.", "symfony/uid": "Required if you'd like to serialize UID objects.", "symfony/yaml": "Required if you'd like to use the YAML metadata format."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"JMS\\Serializer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Library for (de-)serializing data of any complexity; supports XML, and JSON.", "homepage": "http://jmsyst.com/libs/serializer", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "support": {"issues": "https://github.com/schmittjoh/serializer/issues", "source": "https://github.com/schmittjoh/serializer/tree/3.32.5"}, "funding": [{"url": "https://github.com/goetas", "type": "github"}, {"url": "https://github.com/scyzoryck", "type": "github"}], "time": "2025-05-26T15:55:41+00:00"}, {"name": "jms/serializer-bundle", "version": "5.5.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/JMSSerializerBundle.git", "reference": "0538a2bae32a448fdeded53d729308816b5ad2e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/JMSSerializerBundle/zipball/0538a2bae32a448fdeded53d729308816b5ad2e8", "reference": "0538a2bae32a448fdeded53d729308816b5ad2e8", "shasum": ""}, "require": {"jms/metadata": "^2.6", "jms/serializer": "^3.31", "php": "^7.4 || ^8.0", "symfony/config": "^5.4 || ^6.0 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/framework-bundle": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"doctrine/annotations": "^1.14 || ^2.0", "doctrine/coding-standard": "^12.0", "doctrine/orm": "^2.14", "phpunit/phpunit": "^8.0 || ^9.0", "symfony/expression-language": "^5.4 || ^6.0 || ^7.0", "symfony/finder": "^5.4 || ^6.0 || ^7.0", "symfony/form": "^5.4 || ^6.0 || ^7.0", "symfony/stopwatch": "^5.4 || ^6.0 || ^7.0", "symfony/templating": "^5.4 || ^6.0", "symfony/twig-bundle": "^5.4 || ^6.0 || ^7.0", "symfony/uid": "^5.4 || ^6.0 || ^7.0", "symfony/validator": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"symfony/expression-language": "Required for opcache preloading ^5.4 || ^6.0 || ^7.0", "symfony/finder": "Required for cache warmup, supported versions ^5.4 || ^6.0 || ^7.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"JMS\\SerializerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Allows you to easily serialize, and deserialize data of any complexity", "homepage": "http://jmsyst.com/bundles/JMSSerializerBundle", "keywords": ["deserialization", "json", "serialization", "xml"], "support": {"issues": "https://github.com/schmittjoh/JMSSerializerBundle/issues", "source": "https://github.com/schmittjoh/JMSSerializerBundle/tree/5.5.1"}, "funding": [{"url": "https://github.com/goetas", "type": "github"}], "time": "2024-11-06T12:45:22+00:00"}, {"name": "kelunik/certificate", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/kelunik/certificate.git", "reference": "7e00d498c264d5eb4f78c69f41c8bd6719c0199e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kelunik/certificate/zipball/7e00d498c264d5eb4f78c69f41c8bd6719c0199e", "reference": "7e00d498c264d5eb4f78c69f41c8bd6719c0199e", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">=7.0"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "phpunit/phpunit": "^6 | 7 | ^8 | ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Kelunik\\Certificate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Access certificate details and transform between different formats.", "keywords": ["DER", "certificate", "certificates", "openssl", "pem", "x509"], "support": {"issues": "https://github.com/kelunik/certificate/issues", "source": "https://github.com/kelunik/certificate/tree/v1.1.3"}, "time": "2023-02-03T21:26:53+00:00"}, {"name": "laminas/laminas-code", "version": "4.16.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-code.git", "reference": "1793e78dad4108b594084d05d1fb818b85b110af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-code/zipball/1793e78dad4108b594084d05d1fb818b85b110af", "reference": "1793e78dad4108b594084d05d1fb818b85b110af", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"doctrine/annotations": "^2.0.1", "ext-phar": "*", "laminas/laminas-coding-standard": "^3.0.0", "laminas/laminas-stdlib": "^3.18.0", "phpunit/phpunit": "^10.5.37", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.15.0"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "homepage": "https://laminas.dev", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-20T13:15:13+00:00"}, {"name": "league/uri", "version": "7.5.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "81fb5145d2644324614cc532b28efd0215bda430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/81fb5145d2644324614cc532b28efd0215bda430", "reference": "81fb5145d2644324614cc532b28efd0215bda430", "shasum": ""}, "require": {"league/uri-interfaces": "^7.5", "php": "^8.1"}, "conflict": {"league/uri-schemes": "^1.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-fileinfo": "to create Data URI from file contennts", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain", "league/uri-components": "Needed to easily manipulate URI objects components", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.5.1"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:40:02+00:00"}, {"name": "league/uri-interfaces", "version": "7.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1", "psr/http-factory": "^1", "psr/http-message": "^1.1 || ^2.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interfaces and classes for URI representation and interaction", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.5.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:18:47+00:00"}, {"name": "league/uri-parser", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-parser.git", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-parser/zipball/671548427e4c932352d9b9279fdfa345bf63fa00", "reference": "671548427e4c932352d9b9279fdfa345bf63fa00", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.0"}, "suggest": {"ext-intl": "Allow parsing RFC3987 compliant hosts", "league/uri-schemes": "Allow validating and normalizing URI parsing results"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Uri\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "userland URI parser RFC 3986 compliant", "homepage": "https://github.com/thephpleague/uri-parser", "keywords": ["parse_url", "parser", "rfc3986", "rfc3987", "uri", "url"], "support": {"issues": "https://github.com/thephpleague/uri-parser/issues", "source": "https://github.com/thephpleague/uri-parser/tree/master"}, "abandoned": "league/uri-interfaces", "time": "2018-11-22T07:55:51+00:00"}, {"name": "mongodb/mongodb", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/mongodb/mongo-php-library.git", "reference": "3bbe7ba9578724c7e1f47fcd17c881c0995baaad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mongodb/mongo-php-library/zipball/3bbe7ba9578724c7e1f47fcd17c881c0995baaad", "reference": "3bbe7ba9578724c7e1f47fcd17c881c0995baaad", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "ext-mongodb": "^2.1", "php": "^8.1", "psr/log": "^1.1.4|^2|^3", "symfony/polyfill-php85": "^1.32"}, "replace": {"mongodb/builder": "*"}, "require-dev": {"doctrine/coding-standard": "^12.0", "phpunit/phpunit": "^10.5.35", "rector/rector": "^1.2", "squizlabs/php_codesniffer": "^3.7", "vimeo/psalm": "6.5.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"MongoDB\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "MongoDB driver library", "homepage": "https://jira.mongodb.org/browse/PHPLIB", "keywords": ["database", "driver", "mongodb", "persistence"], "support": {"issues": "https://github.com/mongodb/mongo-php-library/issues", "source": "https://github.com/mongodb/mongo-php-library/tree/2.1.0"}, "time": "2025-05-23T10:48:05+00:00"}, {"name": "monolog/monolog", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "c915e2634718dbc8a4a15c61b0e62e7a44e14448"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/c915e2634718dbc8a4a15c61b0e62e7a44e14448", "reference": "c915e2634718dbc8a4a15c61b0e62e7a44e14448", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.4", "phpunit/phpunit": "^10.1", "predis/predis": "^1.1 || ^2", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.5.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2023-10-27T15:32:31+00:00"}, {"name": "nelmio/api-doc-bundle", "version": "4.25.2", "source": {"type": "git", "url": "https://github.com/nelmio/NelmioApiDocBundle.git", "reference": "fc5d60d74b63806f7d6bb80b8393db1a12ae2dbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nelmio/NelmioApiDocBundle/zipball/fc5d60d74b63806f7d6bb80b8393db1a12ae2dbc", "reference": "fc5d60d74b63806f7d6bb80b8393db1a12ae2dbc", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.4", "phpdocumentor/reflection-docblock": "^4.3.4|^5.0", "phpdocumentor/type-resolver": "^1.8.2", "psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.0|^2.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/deprecation-contracts": "^2.1|^3", "symfony/framework-bundle": "^5.4.24|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/options-resolver": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4.10|^6.0|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "zircote/swagger-php": "^4.6.1"}, "conflict": {"zircote/swagger-php": "4.8.7"}, "require-dev": {"api-platform/core": "^2.7.0|^3", "composer/package-versions-deprecated": "*********", "doctrine/annotations": "^2.0", "friendsofphp/php-cs-fixer": "^3.52", "friendsofsymfony/rest-bundle": "^2.8|^3.0", "jms/serializer": "^1.14|^3.0", "jms/serializer-bundle": "^2.3|^3.0|^4.0|^5.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.5", "phpstan/phpstan-symfony": "^1.3", "phpunit/phpunit": "^8.5|^9.6", "symfony/asset": "^5.4|^6.0|^7.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/dom-crawler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/form": "^5.4|^6.0|^7.0", "symfony/phpunit-bridge": "^6.4", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/security-csrf": "^5.4|^6.0|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/templating": "^5.4|^6.0|^7.0", "symfony/twig-bundle": "^5.4|^6.0|^7.0", "symfony/validator": "^5.4|^6.0|^7.0", "willdurand/hateoas-bundle": "^1.0|^2.0"}, "suggest": {"api-platform/core": "For using an API oriented framework.", "doctrine/annotations": "For using doctrine annotations", "friendsofsymfony/rest-bundle": "For using the parameters annotations.", "jms/serializer-bundle": "For describing your models.", "symfony/asset": "For using the Swagger UI.", "symfony/cache": "For using a PSR-6 compatible cache implementation with the API doc generator.", "symfony/form": "For describing your form type models.", "symfony/monolog-bundle": "For using a PSR-3 compatible logger implementation with the API PHP describer.", "symfony/security-csrf": "For using csrf protection tokens in forms.", "symfony/serializer": "For describing your models.", "symfony/twig-bundle": "For using the Swagger UI.", "symfony/validator": "For describing the validation constraints in your models.", "willdurand/hateoas-bundle": "For extracting HATEOAS metadata."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Nelmio\\ApiDocBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://github.com/nelmio/NelmioApiDocBundle/contributors"}], "description": "Generates documentation for your REST API from annotations", "keywords": ["api", "doc", "documentation", "rest"], "support": {"issues": "https://github.com/nelmio/NelmioApiDocBundle/issues", "source": "https://github.com/nelmio/NelmioApiDocBundle/tree/4.25.2"}, "time": "2024-04-06T12:08:11+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "153ae662783729388a584b4361f2545e4d841e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/153ae662783729388a584b4361f2545e4d841e3c", "reference": "153ae662783729388a584b4361f2545e4d841e3c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.8.2"}, "time": "2024-02-23T11:10:43+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.28.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "cd06d6b1a1b3c75b0b83f97577869fd85a3cd4fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/cd06d6b1a1b3c75b0b83f97577869fd85a3cd4fb", "reference": "cd06d6b1a1b3c75b0b83f97577869fd85a3cd4fb", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.28.0"}, "time": "2024-04-03T18:51:33+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "space/mongo-documents", "version": "dev-MongoDB-Json-to-Document", "dist": {"type": "path", "url": "../../space-mongo-documents", "reference": "b0e39cd1890ad0dc13a582e76a7d26b54081e612"}, "require": {"doctrine/mongodb-odm": "^2.6", "doctrine/mongodb-odm-bundle": "^5.3", "jms/serializer-bundle": "^5.5", "mongodb/mongodb": "^2.0", "php": ">=8.1", "symfony/framework-bundle": "^6.4", "symfony/serializer": "^6.4", "symfony/validator": "^6.4"}, "type": "library", "autoload": {"psr-4": {"Space\\MongoDocuments\\": "src/"}}, "license": ["proprietary"], "description": "Shared MongoDB document definitions for Space microservices", "transport-options": {"relative": true}}, {"name": "symfony/asset", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "14b1c0fddb64af6ea626af51bb3c47af9fa19cb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/14b1c0fddb64af6ea626af51bb3c47af9fa19cb7", "reference": "14b1c0fddb64af6ea626af51bb3c47af9fa19cb7", "shasum": ""}, "require": {"php": ">=8.1"}, "conflict": {"symfony/http-foundation": "<5.4"}, "require-dev": {"symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/cache", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "b59bbf9c093b592d77110f9ee70c74dff89294cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/b59bbf9c093b592d77110f9ee70c74dff89294cb", "reference": "b59bbf9c093b592d77110f9ee70c74dff89294cb", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-27T13:27:42+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "2c9db6509a1b21dad229606897639d3284f54b2a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/2c9db6509a1b21dad229606897639d3284f54b2a", "reference": "2c9db6509a1b21dad229606897639d3284f54b2a", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.4.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/config", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "18ac9da3106222dde9fc9e09ec016e5de9d2658f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/18ac9da3106222dde9fc9e09ec016e5de9d2658f", "reference": "18ac9da3106222dde9fc9e09ec016e5de9d2658f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<5.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-27T19:47:45+00:00"}, {"name": "symfony/console", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a2708a5da5c87d1d0d52937bdeac625df659e11f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a2708a5da5c87d1d0d52937bdeac625df659e11f", "reference": "a2708a5da5c87d1d0d52937bdeac625df659e11f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-29T19:07:53+00:00"}, {"name": "symfony/dependency-injection", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "31417777509923b22de5c6fb6b3ffcdebde37cb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/31417777509923b22de5c6fb6b3ffcdebde37cb5", "reference": "31417777509923b22de5c6fb6b3ffcdebde37cb5", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.2.10|^7.0"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.1", "symfony/finder": "<5.4", "symfony/proxy-manager-bridge": "<6.3", "symfony/yaml": "<5.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.1|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-27T22:00:14+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/7c3aff79d10325257a001fcf92d991f24fc967cf", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-23T14:45:45+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "b880cebd0689a466cc4afef30009a54162c003c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/b880cebd0689a466cc4afef30009a54162c003c4", "reference": "b880cebd0689a466cc4afef30009a54162c003c4", "shasum": ""}, "require": {"doctrine/event-manager": "^1.2|^2", "doctrine/persistence": "^2.5|^3.1|^4", "php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"doctrine/dbal": "<2.13.1", "doctrine/lexer": "<1.1", "doctrine/orm": "<2.15", "symfony/cache": "<5.4", "symfony/dependency-injection": "<6.2", "symfony/form": "<5.4.38|>=6,<6.4.6|>=7,<7.0.6", "symfony/http-foundation": "<6.3", "symfony/http-kernel": "<6.2", "symfony/lock": "<6.3", "symfony/messenger": "<5.4", "symfony/property-info": "<5.4", "symfony/security-bundle": "<5.4", "symfony/security-core": "<6.4", "symfony/validator": "<6.4"}, "require-dev": {"doctrine/collections": "^1.0|^2.0", "doctrine/data-fixtures": "^1.1|^2", "doctrine/dbal": "^2.13.1|^3|^4", "doctrine/orm": "^2.15|^3", "psr/log": "^1|^2|^3", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^6.2|^7.0", "symfony/doctrine-messenger": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/form": "^5.4.38|^6.4.6|^7.0.6", "symfony/http-kernel": "^6.3|^7.0", "symfony/lock": "^6.3|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/proxy-manager-bridge": "^6.4", "symfony/security-core": "^6.4|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Doctrine with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-bridge/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-09T14:04:38+00:00"}, {"name": "symfony/dotenv", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "f6f0a3dd102915b4c5bfdf4f4e3139a8cbf477a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/f6f0a3dd102915b4c5bfdf4f4e3139a8cbf477a0", "reference": "f6f0a3dd102915b4c5bfdf4f4e3139a8cbf477a0", "shasum": ""}, "require": {"php": ">=8.1"}, "conflict": {"symfony/console": "<5.4", "symfony/process": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-08T17:53:17+00:00"}, {"name": "symfony/error-handler", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "64db1c1802e3a4557e37ba33031ac39f452ac5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/64db1c1802e3a4557e37ba33031ac39f452ac5d4", "reference": "64db1c1802e3a4557e37ba33031ac39f452ac5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-19T11:56:30+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "ae9d3a6f3003a6caf56acd7466d8d52378d44fef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ae9d3a6f3003a6caf56acd7466d8d52378d44fef", "reference": "ae9d3a6f3003a6caf56acd7466d8d52378d44fef", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "4e64b49bf370ade88e567de29465762e316e4224"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/4e64b49bf370ade88e567de29465762e316e4224", "reference": "4e64b49bf370ade88e567de29465762e316e4224", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.4.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "9919b5509ada52cc7f66f9a35c86a4a29955c9d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/9919b5509ada52cc7f66f9a35c86a4a29955c9d3", "reference": "9919b5509ada52cc7f66f9a35c86a4a29955c9d3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-21T19:36:20+00:00"}, {"name": "symfony/finder", "version": "v6.4.0", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "11d736e97f116ac375a81f96e662911a34cd50ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/11d736e97f116ac375a81f96e662911a34cd50ce", "reference": "11d736e97f116ac375a81f96e662911a34cd50ce", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-10-31T17:30:12+00:00"}, {"name": "symfony/flex", "version": "v2.4.5", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "b0a405f40614c9f584b489d54f91091817b0e26e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/b0a405f40614c9f584b489d54f91091817b0e26e", "reference": "b0a405f40614c9f584b489d54f91091817b0e26e", "shasum": ""}, "require": {"composer-plugin-api": "^2.1", "php": ">=8.0"}, "require-dev": {"composer/composer": "^2.1", "symfony/dotenv": "^5.4|^6.0", "symfony/filesystem": "^5.4|^6.0", "symfony/phpunit-bridge": "^5.4|^6.0", "symfony/process": "^5.4|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v2.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-02T08:16:47+00:00"}, {"name": "symfony/framework-bundle", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "49093e57c7eea2ecd1603b0218c797fc37514ae9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/49093e57c7eea2ecd1603b0218c797fc37514ae9", "reference": "49093e57c7eea2ecd1603b0218c797fc37514ae9", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.1", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^6.1|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.1|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^6.4|^7.0"}, "conflict": {"doctrine/annotations": "<1.13.1", "doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/asset": "<5.4", "symfony/asset-mapper": "<6.4", "symfony/clock": "<6.3", "symfony/console": "<5.4|>=7.0", "symfony/dom-crawler": "<6.4", "symfony/dotenv": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<6.3", "symfony/lock": "<5.4", "symfony/mailer": "<5.4", "symfony/messenger": "<6.3", "symfony/mime": "<6.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4", "symfony/scheduler": "<6.4.4|>=7.0.0,<7.0.4", "symfony/security-core": "<5.4", "symfony/security-csrf": "<5.4", "symfony/serializer": "<6.4", "symfony/stopwatch": "<5.4", "symfony/translation": "<6.4", "symfony/twig-bridge": "<5.4", "symfony/twig-bundle": "<5.4", "symfony/validator": "<6.4", "symfony/web-profiler-bundle": "<6.4", "symfony/workflow": "<6.4"}, "require-dev": {"doctrine/annotations": "^1.13.1|^2", "doctrine/persistence": "^1.3|^2|^3", "dragonmantank/cron-expression": "^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/asset": "^5.4|^6.0|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/console": "^5.4.9|^6.0.9|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/dotenv": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/form": "^5.4|^6.0|^7.0", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/http-client": "^6.3|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/mailer": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.3|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/notifier": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0", "symfony/scheduler": "^6.4.4|^7.0.4", "symfony/security-bundle": "^5.4|^6.0|^7.0", "symfony/semaphore": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/string": "^5.4|^6.0|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/twig-bundle": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^5.4|^6.0|^7.0", "symfony/workflow": "^6.4|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0", "twig/twig": "^2.10|^3.0.4"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-23T16:06:09+00:00"}, {"name": "symfony/http-client", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "6a46c0ea9b099f9a5132d560a51833ffcbd5b0d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/6a46c0ea9b099f9a5132d560a51833ffcbd5b0d9", "reference": "6a46c0ea9b099f9a5132d560a51833ffcbd5b0d9", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "^3.4.1", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-foundation": "<6.3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-01T20:35:50+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "b6b5c876b3a4ed74460e2c5ac53bbce2f12e2a7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/b6b5c876b3a4ed74460e2c5ac53bbce2f12e2a7e", "reference": "b6b5c876b3a4ed74460e2c5ac53bbce2f12e2a7e", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.4.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-01T18:51:09+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "ebc713bc6e6f4b53f46539fc158be85dfcd77304"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/ebc713bc6e6f4b53f46539fc158be85dfcd77304", "reference": "ebc713bc6e6f4b53f46539fc158be85dfcd77304", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.3"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.3|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-08T15:01:18+00:00"}, {"name": "symfony/http-kernel", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "060038863743fd0cd982be06acecccf246d35653"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/060038863743fd0cd982be06acecccf246d35653", "reference": "060038863743fd0cd982be06acecccf246d35653", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<6.1", "symfony/console": "<5.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<5.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.3", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/config": "^6.1|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.5|^6.0.5|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.4|^7.0.4", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-exporter": "^6.2|^7.0", "twig/twig": "^2.13|^3.0.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-03T06:09:15+00:00"}, {"name": "symfony/intl", "version": "v6.4.15", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "b1d5e8d82615b60f229216edfee0b59e2ef66da6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/b1d5e8d82615b60f229216edfee0b59e2ef66da6", "reference": "b1d5e8d82615b60f229216edfee0b59e2ef66da6", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides access to the localization data of the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v6.4.15"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-08T15:28:48+00:00"}, {"name": "symfony/monolog-bridge", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "db7468152b27242f1a4d10fabe278a2cfaa4eac0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/db7468152b27242f1a4d10fabe278a2cfaa4eac0", "reference": "db7468152b27242f1a4d10fabe278a2cfaa4eac0", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1|^2|^3", "php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<5.4", "symfony/http-foundation": "<5.4", "symfony/security-core": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/mailer": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/security-core": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Monolog with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-01T11:49:25+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.10.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1 || ^2.0 || ^3.0", "php": ">=7.2.5", "symfony/config": "^5.4 || ^6.0 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/http-kernel": "^5.4 || ^6.0 || ^7.0", "symfony/monolog-bridge": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/phpunit-bridge": "^6.3 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "https://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-06T17:08:13+00:00"}, {"name": "symfony/options-resolver", "version": "v6.4.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "22301f0e7fdeaacc14318928612dee79be99860e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/22301f0e7fdeaacc14318928612dee79be99860e", "reference": "22301f0e7fdeaacc14318928612dee79be99860e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v6.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-08-08T10:16:24+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/32a9da87d7b3245e09ac426c83d334ae9f06f80f", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/bc45c394692b948b4d383a08d7753968bed9a83d", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/86fcae159633351e5fd145d1c47de6c528f8caff", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-php80": "^1.14"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php84", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php84.git", "reference": "000df7860439609837bbe28670b0be15783b7fbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php84/zipball/000df7860439609837bbe28670b0be15783b7fbf", "reference": "000df7860439609837bbe28670b0be15783b7fbf", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php84\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.4+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php84/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-20T12:04:08+00:00"}, {"name": "symfony/polyfill-php85", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php85.git", "reference": "6fedf31ce4e3648f4ff5ca58bfd53127d38f05fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php85/zipball/6fedf31ce4e3648f4ff5ca58bfd53127d38f05fd", "reference": "6fedf31ce4e3648f4ff5ca58bfd53127d38f05fd", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php85\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.5+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php85/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-02T08:40:52+00:00"}, {"name": "symfony/property-access", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "304e5559cd3ebcfb5d743bd21a3d25eb3b7a6ae7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/304e5559cd3ebcfb5d743bd21a3d25eb3b7a6ae7", "reference": "304e5559cd3ebcfb5d743bd21a3d25eb3b7a6ae7", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/property-info": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/cache": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-19T11:56:30+00:00"}, {"name": "symfony/property-info", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "893120c46f8b78086d5bee90f91d6ff85e4057f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/893120c46f8b78086d5bee90f91d6ff85e4057f2", "reference": "893120c46f8b78086d5bee90f91d6ff85e4057f2", "shasum": ""}, "require": {"php": ">=8.1", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/dependency-injection": "<5.4", "symfony/serializer": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-27T22:00:14+00:00"}, {"name": "symfony/routing", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "f2591fd1f8c6e3734656b5d6b3829e8bf81f507c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/f2591fd1f8c6e3734656b5d6b3829e8bf81f507c", "reference": "f2591fd1f8c6e3734656b5d6b3829e8bf81f507c", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^6.2|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-28T13:28:49+00:00"}, {"name": "symfony/runtime", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/runtime.git", "reference": "5682281d26366cd3bf0648cec69de0e62cca7fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/runtime/zipball/5682281d26366cd3bf0648cec69de0e62cca7fa0", "reference": "5682281d26366cd3bf0648cec69de0e62cca7fa0", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=8.1"}, "conflict": {"symfony/dotenv": "<5.4"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "symfony/console": "^5.4.9|^6.0.9|^7.0", "symfony/dotenv": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Component\\Runtime\\Internal\\ComposerPlugin"}, "autoload": {"psr-4": {"Symfony\\Component\\Runtime\\": "", "Symfony\\Runtime\\Symfony\\Component\\": "Internal/"}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Enables decoupling PHP applications from global state", "homepage": "https://symfony.com", "keywords": ["runtime"], "support": {"source": "https://github.com/symfony/runtime/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/serializer", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "3697adf91f83516c86b4912c08c28084711ed560"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/3697adf91f83516c86b4912c08c28084711ed560", "reference": "3697adf91f83516c86b4912c08c28084711ed560", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<5.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4.24|>=6,<6.2.11", "symfony/uid": "<5.4", "symfony/validator": "<6.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/form": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.26|^6.3|^7.0", "symfony/property-info": "^5.4.24|^6.2.11|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-27T22:00:14+00:00"}, {"name": "symfony/service-contracts", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "11bbf19a0fb7b36345861e85c5768844c552906e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/11bbf19a0fb7b36345861e85c5768844c552906e", "reference": "11bbf19a0fb7b36345861e85c5768844c552906e", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-19T21:51:00+00:00"}, {"name": "symfony/string", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9", "reference": "4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-01T13:16:41+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "43810bdb2ddb5400e5c5e778e27b210a0ca83b6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/43810bdb2ddb5400e5c5e778e27b210a0ca83b6b", "reference": "43810bdb2ddb5400e5c5e778e27b210a0ca83b6b", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.4.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/twig-bridge", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "f150e06e2fbe8004dbcaa66a46bf20b2b3a99308"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/f150e06e2fbe8004dbcaa66a46bf20b2b3a99308", "reference": "f150e06e2fbe8004dbcaa66a46bf20b2b3a99308", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/translation-contracts": "^2.5|^3", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<5.4", "symfony/form": "<6.3", "symfony/http-foundation": "<5.4", "symfony/http-kernel": "<6.4", "symfony/mime": "<6.2", "symfony/serializer": "<6.4", "symfony/translation": "<5.4", "symfony/workflow": "<5.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^5.4|^6.0|^7.0", "symfony/asset-mapper": "^6.3|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/form": "^6.4|^7.0", "symfony/html-sanitizer": "^6.1|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^5.4|^6.0|^7.0", "symfony/security-csrf": "^5.4|^6.0|^7.0", "symfony/security-http": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^6.1|^7.0", "symfony/web-link": "^5.4|^6.0|^7.0", "symfony/workflow": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-28T21:00:57+00:00"}, {"name": "symfony/twig-bundle", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "f60ba43a09d88395d05797af982588b57331ff4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/f60ba43a09d88395d05797af982588b57331ff4d", "reference": "f60ba43a09d88395d05797af982588b57331ff4d", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "php": ">=8.1", "symfony/config": "^6.1|^7.0", "symfony/dependency-injection": "^6.1|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^6.2", "symfony/twig-bridge": "^6.4", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"symfony/framework-bundle": "<5.4", "symfony/translation": "<5.4"}, "require-dev": {"symfony/asset": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/form": "^5.4|^6.0|^7.0", "symfony/framework-bundle": "^5.4|^6.0|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/web-link": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-15T11:23:52+00:00"}, {"name": "symfony/validator", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "ca1d78e8677e966e307a63799677b64b194d735d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/ca1d78e8677e966e307a63799677b64b194d735d", "reference": "ca1d78e8677e966e307a63799677b64b194d735d", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3|^7.0.3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-27T22:00:14+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "95bd2706a97fb875185b51ecaa6112ec184233d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/95bd2706a97fb875185b51ecaa6112ec184233d4", "reference": "95bd2706a97fb875185b51ecaa6112ec184233d4", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-19T11:56:30+00:00"}, {"name": "symfony/var-exporter", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "20888cf4d11de203613515cf0587828bf5af0fe7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/20888cf4d11de203613515cf0587828bf5af0fe7", "reference": "20888cf4d11de203613515cf0587828bf5af0fe7", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-20T21:07:14+00:00"}, {"name": "symfony/yaml", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "d75715985f0f94f978e3a8fa42533e10db921b90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/d75715985f0f94f978e3a8fa42533e10db921b90", "reference": "d75715985f0f94f978e3a8fa42533e10db921b90", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "twig/twig", "version": "v3.8.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "9d15f0ac07f44dc4217883ec6ae02fd555c6f71d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/9d15f0ac07f44dc4217883ec6ae02fd555c6f71d", "reference": "9d15f0ac07f44dc4217883ec6ae02fd555c6f71d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php80": "^1.22"}, "require-dev": {"psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.3|^7.0"}, "type": "library", "autoload": {"psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.8.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2023-11-21T18:54:41+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "zircote/swagger-php", "version": "4.8.6", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "fe45a0e98a156999ddafc31c9ad333fdcadbcb2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/fe45a0e98a156999ddafc31c9ad333fdcadbcb2d", "reference": "fe45a0e98a156999ddafc31c9ad333fdcadbcb2d", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.2", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2 || ^3", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^1.7 || ^2.0", "friendsofphp/php-cs-fixer": "^2.17 || ^3.47.1", "phpstan/phpstan": "^1.6", "phpunit/phpunit": ">=8", "vimeo/psalm": "^4.23"}, "suggest": {"doctrine/annotations": "^1.7 || ^2.0"}, "bin": ["bin/openapi"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://bfanger.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://radebatz.net"}], "description": "swagger-php - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/4.8.6"}, "time": "2024-03-15T01:13:55+00:00"}], "packages-dev": [{"name": "composer/pcre", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "5b16e25a5355f1f3afdfc2f954a0a80aec4826a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/5b16e25a5355f1f3afdfc2f954a0a80aec4826a8", "reference": "5b16e25a5355f1f3afdfc2f954a0a80aec4826a8", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.1.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-03-19T10:26:25+00:00"}, {"name": "composer/semver", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/35e8d0af4486141bc745f23a29cc2091eb624a32", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-08-31T09:50:34+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "4f988f8fdf580d53bdb2d1278fe93d1ed5462255"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/4f988f8fdf580d53bdb2d1278fe93d1ed5462255", "reference": "4f988f8fdf580d53bdb2d1278fe93d1ed5462255", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.4"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-03-26T18:29:49+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.53.0", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer.git", "reference": "69a19093a9ded8d1baac62ed6c009b8bc148d008"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/PHP-CS-Fixer/zipball/69a19093a9ded8d1baac62ed6c009b8bc148d008", "reference": "69a19093a9ded8d1baac62ed6c009b8bc148d008", "shasum": ""}, "require": {"composer/semver": "^3.4", "composer/xdebug-handler": "^3.0.3", "ext-filter": "*", "ext-json": "*", "ext-tokenizer": "*", "php": "^7.4 || ^8.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/event-dispatcher": "^5.4 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0", "symfony/finder": "^5.4 || ^6.0 || ^7.0", "symfony/options-resolver": "^5.4 || ^6.0 || ^7.0", "symfony/polyfill-mbstring": "^1.28", "symfony/polyfill-php80": "^1.28", "symfony/polyfill-php81": "^1.28", "symfony/process": "^5.4 || ^6.0 || ^7.0", "symfony/stopwatch": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"facile-it/paraunit": "^1.3 || ^2.0", "infection/infection": "^0.27.11", "justinrainbow/json-schema": "^5.2", "keradus/cli-executor": "^2.1", "mikey179/vfsstream": "^1.6.11", "php-coveralls/php-coveralls": "^2.7", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.4", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.4", "phpunit/phpunit": "^9.6 || ^10.5.5 || ^11.0.2", "symfony/var-dumper": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "keywords": ["Static code analysis", "fixer", "standards", "static analysis"], "support": {"issues": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/issues", "source": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/tree/v3.53.0"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2024-04-08T15:03:00+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "nikic/php-parser", "version": "v5.0.2", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "139676794dc1e9231bf7bcd123cfc0c99182cb13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/139676794dc1e9231bf7bcd123cfc0c99182cb13", "reference": "139676794dc1e9231bf7bcd123cfc0c99182cb13", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.0.2"}, "time": "2024-03-05T20:51:40+00:00"}, {"name": "phpstan/phpstan", "version": "1.10.66", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "94779c987e4ebd620025d9e5fdd23323903950bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/94779c987e4ebd620025d9e5fdd23323903950bd", "reference": "94779c987e4ebd620025d9e5fdd23323903950bd", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2024-03-28T16:17:31+00:00"}, {"name": "rector/rector", "version": "0.19.8", "source": {"type": "git", "url": "https://github.com/rectorphp/rector.git", "reference": "de3b3bb159abd704b144aa86fb244f7f1f4ac947"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rectorphp/rector/zipball/de3b3bb159abd704b144aa86fb244f7f1f4ac947", "reference": "de3b3bb159abd704b144aa86fb244f7f1f4ac947", "shasum": ""}, "require": {"php": "^7.2|^8.0", "phpstan/phpstan": "^1.10.56"}, "conflict": {"rector/rector-doctrine": "*", "rector/rector-downgrade-php": "*", "rector/rector-phpunit": "*", "rector/rector-symfony": "*"}, "bin": ["bin/rector"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Instant Upgrade and Automated Refactoring of any PHP code", "keywords": ["automation", "dev", "migration", "refactoring"], "support": {"issues": "https://github.com/rectorphp/rector/issues", "source": "https://github.com/rectorphp/rector/tree/0.19.8"}, "funding": [{"url": "https://github.com/tomasvotruba", "type": "github"}], "time": "2024-02-05T10:59:13+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "symfony/browser-kit", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "495ffa2e6d17e199213f93768efa01af32bbf70e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/495ffa2e6d17e199213f93768efa01af32bbf70e", "reference": "495ffa2e6d17e199213f93768efa01af32bbf70e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/dom-crawler": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/css-selector", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "ee0f7ed5cf298cc019431bb3b3977ebc52b86229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/ee0f7ed5cf298cc019431bb3b3977ebc52b86229", "reference": "ee0f7ed5cf298cc019431bb3b3977ebc52b86229", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/debug-bundle", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/debug-bundle.git", "reference": "425c7760a4e6fdc6cb643c791d32277037c971df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug-bundle/zipball/425c7760a4e6fdc6cb643c791d32277037c971df", "reference": "425c7760a4e6fdc6cb643c791d32277037c971df", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=8.1", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/twig-bridge": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/config": "<5.4", "symfony/dependency-injection": "<5.4"}, "require-dev": {"symfony/config": "^5.4|^6.0|^7.0", "symfony/web-profiler-bundle": "^5.4|^6.0|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\DebugBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Symfony VarDumper component and the ServerLogCommand from MonologBridge into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug-bundle/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/dom-crawler", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "f0e7ec3fa17000e2d0cb4557b4b47c88a6a63531"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/f0e7ec3fa17000e2d0cb4557b4b47c88a6a63531", "reference": "f0e7ec3fa17000e2d0cb4557b4b47c88a6a63531", "shasum": ""}, "require": {"masterminds/html5": "^2.6", "php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-07T09:17:57+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.58.0", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "c4f8d2c5d55950e1a49e822efc83a8511bee8a36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/c4f8d2c5d55950e1a49e822efc83a8511bee8a36", "reference": "c4f8d2c5d55950e1a49e822efc83a8511bee8a36", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "nikic/php-parser": "^4.18|^5.0", "php": ">=8.1", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.2|^3", "symfony/filesystem": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "conflict": {"doctrine/doctrine-bundle": "<2.10", "doctrine/orm": "<2.15"}, "require-dev": {"composer/semver": "^3.0", "doctrine/doctrine-bundle": "^2.5.0", "doctrine/orm": "^2.15|^3", "symfony/http-client": "^6.4|^7.0", "symfony/phpunit-bridge": "^6.4.1|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.0|^4.x-dev"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "dev", "generator", "scaffold", "scaffolding"], "support": {"issues": "https://github.com/symfony/maker-bundle/issues", "source": "https://github.com/symfony/maker-bundle/tree/v1.58.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-06T15:08:12+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "6106ae85a0e3ed509d339b7f924788c9cc4e7cfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/6106ae85a0e3ed509d339b7f924788c9cc4e7cfb", "reference": "6106ae85a0e3ed509d339b7f924788c9cc4e7cfb", "shasum": ""}, "require": {"php": ">=7.2.5"}, "conflict": {"phpunit/phpunit": "<7.5|9.1.2"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3.0", "symfony/error-handler": "^5.4|^6.4|^7.0", "symfony/polyfill-php81": "^1.27"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"thanks": {"url": "https://github.com/sebastian<PERSON>mann/phpunit", "name": "phpunit/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/", "/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides utilities for PHPUnit, especially user deprecation notices management", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/phpunit-bridge/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-09T08:35:42+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "c565ad1e63f30e7477fc40738343c62b40bc672d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/c565ad1e63f30e7477fc40738343c62b40bc672d", "reference": "c565ad1e63f30e7477fc40738343c62b40bc672d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/process", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "710e27879e9be3395de2b98da3f52a946039f297"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/710e27879e9be3395de2b98da3f52a946039f297", "reference": "710e27879e9be3395de2b98da3f52a946039f297", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-20T12:31:00+00:00"}, {"name": "symfony/stopwatch", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "416596166641f1f728b0a64f5b9dd07cceb410c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/416596166641f1f728b0a64f5b9dd07cceb410c1", "reference": "416596166641f1f728b0a64f5b9dd07cceb410c1", "shasum": ""}, "require": {"php": ">=8.1", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:35:58+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "a69d7124bfb2e15638ba0a1be94f0845d8d05ee4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/a69d7124bfb2e15638ba0a1be94f0845d8d05ee4", "reference": "a69d7124bfb2e15638ba0a1be94f0845d8d05ee4", "shasum": ""}, "require": {"php": ">=8.1", "symfony/config": "^5.4|^6.0|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/twig-bundle": "^5.4|^6.0", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"symfony/form": "<5.4", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/twig-bundle": ">=7.0"}, "require-dev": {"symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a development tool that gives detailed information about the execution of any request", "homepage": "https://symfony.com", "keywords": ["dev"], "support": {"source": "https://github.com/symfony/web-profiler-bundle/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-22T20:27:10+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^8.0", "ext-ctype": "*", "ext-iconv": "*"}, "platform-dev": [], "plugin-api-version": "2.2.0"}