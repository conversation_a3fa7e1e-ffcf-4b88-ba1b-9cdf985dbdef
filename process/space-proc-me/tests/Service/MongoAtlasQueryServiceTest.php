<?php

namespace App\Tests\Service;

use App\Connector\MongoAtlasApiConnector;
use App\Helper\WSResponse;
use Space\MongoDocuments\Model\MongoQueryModel;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class MongoAtlasQueryServiceTest extends TestCase
{
    private MongoAtlasQueryService $service;

    private MongoAtlasApiConnector $connector;

    private NormalizerInterface $normalizer;

    public function setUp(): void
    {
        parent::setUp();

        $this->connector = $this->createMock(MongoAtlasApiConnector::class);
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info')
        ;
        $logger->expects($this->any())
            ->method('error')
        ;
        $this->connector->setLogger($logger);

        $this->normalizer = $this->createMock(NormalizerInterface::class);

        $this->service = new MongoAtlasQueryService(
            $this->normalizer,
            $this->connector,
            'testDataSource',
            'testDatabase'
        );
        $this->service->setLogger($logger);
    }

    public function testBuildQuery(): void
    {
        $query = MongoAtlasQueryService::buildQuery('testDataSource', 'testDatabase');

        $this->assertInstanceOf(MongoQueryModel::class, $query);
        $this->assertEquals('testDataSource', $query->getDataSource());
        $this->assertEquals('testDatabase', $query->getDatabase());
    }

    public function testExecuteException(): void
    {
        $this->connector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception());

        $response = $this->service->find('testCollection', [
            'field1' => 'value1',
            'field2' => 'value2',
        ]);

        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testFind(): void
    {
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('find')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'find']])
            ->willReturn([
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', 'testEndpoint', ['json' => [
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->find('testCollection', [
            'field1' => 'value1',
            'field2' => 'value2',
        ]);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    public function testAggregateOKResponse(): void
    {
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('aggregate')
            ->willReturn('aggregate');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'aggregate']])
            ->willReturn([
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', 'aggregate', ['json' => [
                'dataSource' => 'testDataSource',
                'database' => 'testDatabase',
                'collection' => 'testCollection',
                'filter' => [
                    'field1' => 'value1',
                    'field2' => 'value2',
                ],
            ]])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->aggregate('testCollection', [
            'field1' => 'value1',
            'field2' => 'value2',
        ]);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    public function testUpdateManyOKResponse(): void
    {
        $this->updateTest('updateMany');
    }

    public function testUpdateOneOKResponse(): void
    {
        $this->updateTest('updateOne');
    }

    private function updateTest(string $action): void
    {
        $filter = ['field1' => 'value1', 'field2' => 'value2'];
        $fields = [];
        $mongoQuery = [
            'dataSource' => 'testDataSource',
            'database' => 'testDatabase',
            'collection' => 'testCollection',
            'filter' => $filter,
            '$set' => $fields,
        ];
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with($action)
            ->willReturn($action);

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', $action]])
            ->willReturn($mongoQuery);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', $action, ['json' => $mongoQuery])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->$action('testCollection', $filter, $fields);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    public function testUpdatePush(): void
    {
        $action = 'updateOne';
        $filter = ['field1' => 'value1', 'field2' => 'value2'];
        $fields = [];
        $mongoQuery = [
            'dataSource' => 'testDataSource',
            'database' => 'testDatabase',
            'collection' => 'testCollection',
            'filter' => $filter,
            '$push' => $fields,
        ];
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with($action)
            ->willReturn($action);

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', $action]])
            ->willReturn($mongoQuery);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', $action, ['json' => $mongoQuery])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->updatePush('testCollection', $filter, $fields);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    public function testInsertOneOKResponse(): void
    {
        $document['document'] = ['userId' => '1234567'];
        $this->insertTest('insertOne', $document);
    }

    public function testInsertManyOKResponse(): void
    {
        $documents['documents'] = [['userId' => '1234567'], ['userId' => '7654321'], ['userId' => '975312']];
        $this->insertTest('insertMany', $documents);
    }

    private function insertTest(string $action, $documents): void
    {
        $mongoQuery = [
            'dataSource' => 'testDataSource',
            'database' => 'testDatabase',
            'collection' => 'testCollection',
            $documents,
        ];
        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with($action)
            ->willReturn($action);

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', $action]])
            ->willReturn($mongoQuery);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with('POST', $action, ['json' => $mongoQuery])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->$action('testCollection', $documents);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }
}
