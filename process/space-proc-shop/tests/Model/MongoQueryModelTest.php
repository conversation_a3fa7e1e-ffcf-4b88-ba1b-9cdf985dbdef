<?php

namespace App\Tests\Model;

use Space\MongoDocuments\Model\MongoQueryModel;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class MongoQueryModelTest extends KernelTestCase
{
    public function testModel(): void
    {
        $defaultValue = $expected = ['testKey' => 'testValue'];
        $mongoModel = new MongoQueryModel();
        $mongoModel->setFilter($defaultValue);
        $mongoModel->setDocument($defaultValue);
        $mongoModel->setUpdate($defaultValue);
        $mongoModel->setPipeline($defaultValue);
        $mongoModel->setDocuments($defaultValue);
        $mongoModel->setUpsert(true);

        static::assertEquals($expected, $mongoModel->getFilter());
        static::assertEquals($expected, $mongoModel->getDocument());
        static::assertEquals($expected, $mongoModel->getUpdate());
        static::assertEquals($expected, $mongoModel->getPipeline());
        static::assertEquals($expected, $mongoModel->getDocuments());
        static::assertEquals(true, $mongoModel->getUpsert());
    }
}
