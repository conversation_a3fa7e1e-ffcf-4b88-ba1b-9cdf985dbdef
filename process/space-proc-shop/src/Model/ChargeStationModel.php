<?php

namespace App\Model;

class ChargeStationModel
{
    private string $id;
    private string $chargeStationId;
    private string $locationId;
    private POIModel $poi;
    private ChargeStationAddressModel $address;
    private CoordinateV2Model $position;
    private array $connectors;
    private array $cslProviderData;
    private string $brand;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getChargeStationId(): ?string
    {
        return $this->chargeStationId;
    }

    public function setChargeStationId(string $chargeStationId): void
    {
        $this->chargeStationId = $chargeStationId;
    }

    public function getLocationId(): ?string
    {
        return $this->locationId;
    }

    public function setLocationId(string $locationId): void
    {
        $this->locationId = $locationId;
    }

    public function getPoi(): POIModel
    {
        return $this->poi;
    }

    public function setPoi(POIModel $poi): void
    {
        $this->poi = $poi;
    }

    public function getAddress(): ChargeStationAddressModel
    {
        return $this->address;
    }

    public function setAddress(ChargeStationAddressModel $address): void
    {
        $this->address = $address;
    }

    public function getPosition(): CoordinateV2Model
    {
        return $this->position;
    }

    public function setPosition(CoordinateV2Model $position): void
    {
        $this->position = $position;
    }

    public function getConnectors(): array
    {
        return $this->connectors;
    }

    public function setConnectors(array $connectors): void
    {
        $this->connectors = $connectors;
    }

    public function getCslProviderData(): array
    {
        return $this->cslProviderData;
    }

    public function setCslProviderData(array $cslProviderData): void
    {
        $this->cslProviderData = $cslProviderData;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(string $brand): void
    {
        $this->brand = $brand;
    }
}