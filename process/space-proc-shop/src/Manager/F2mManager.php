<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Model\ChargeStationAddressModel;
use App\Model\ChargeStationModel;
use App\Model\CoordinateV2Model;
use App\Model\POIModel;
use App\Service\F2mService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

/**
 * F2mManager
 */
class F2mManager
{
    use LoggerTrait;

    public function __construct(private F2mService $f2mService)
    {
    }

    /**
     * get charging stationg data
     */
    public function search(array $params)
    {
        try {
            $this->logger->info('Start of calling F2m charging station locator');
            $response = $this->f2mService->search($params);
            $this->logger->info('End F2m charging station locator');

            if (Response::HTTP_OK == $response->getCode()) {
                $brand = $params['brand'];
                Assert::isArray($response->getData());
                Assert::keyExists($response->getData(), 'success');
                $chargingStations = $response->getData()['success'] ?? [];
                $data = array_map(function ($responseData) use ($brand) {
                    return $this->mapChargingStationData($responseData, $brand);
                }, $chargingStations);
                return (new SuccessResponse($data));
            } else {
                $responseData = $response->getData();
                return (new ErrorResponse($responseData['error']['message'], Response::HTTP_BAD_REQUEST));
            }
        } catch (\Exception $e) {
            $this->logger->error('Manager :: Error of getting F2m charging station locator ' . $e->getMessage() . '. Code error :' . Response::HTTP_BAD_REQUEST);
            return (new ErrorResponse("Error getting a response from F2M charging station locator", $e->getCode()));
        }
    }

    private function isValidResponse(array $response): bool
    {
        return isset($response['content']) && $response['code'] && $response['code'] == Response::HTTP_OK;
    }

    private function mapChargingStationData(array $responseData, string $brand): ChargeStationModel
    {
        $chargingStationg = new ChargeStationModel();
        $chargingStationg->setId($responseData['charging_station_id']);
        $chargingStationg->setChargeStationId($responseData['charging_station_id']);
        $chargingStationg->setLocationId($responseData['location_id']);
        $chargingStationg->setPoi($this->mapPoiData());
        $chargingStationg->setAddress($this->mapAddressData());
        $chargingStationg->setPosition($this->mapPosition($responseData['coordinates']));
        $chargingStationg->setConnectors($this->mapConnectorsData());
        $chargingStationg->setCslProviderData(['f2m' => null]);
        $chargingStationg->setBrand($brand);

        return $chargingStationg;
    }

    private function mapPoiData(): POIModel
    {
        return new POIModel();
    }

    private function mapPosition($responseData): CoordinateV2Model
    {
        $position = new CoordinateV2Model();
        $position->setLatitude($responseData['latitude']);
        $position->setLongitude($responseData['longitude']);
        return $position;
    }

    private function mapAddressData(): ChargeStationAddressModel
    {
        return new ChargeStationAddressModel();
    }

    private function mapConnectorsData(): array
    {
        return [
            [
                'type' => null,
                'compatible' => true,
                'powerLevel' => [],
                'total' => 0,
                'availability' => [],
            ]
        ];
    }
}
